# Core dependencies for Instagram sentiment analysis notebook
jupyter==1.0.0
notebook==7.0.6
ipywidgets==8.1.1

# Data processing
pandas==2.0.3
numpy==1.24.3

# Google Play Store scraping
google-play-scraper==1.2.4

# Machine learning and NLP
transformers==4.35.2
torch==2.1.1
datasets==2.14.6
scikit-learn==1.3.2

# Language detection
langdetect==1.0.9

# Visualization
matplotlib==3.7.2
seaborn==0.12.2

# Progress bars
tqdm==4.66.1

# Web scraping utilities
requests==2.31.0
beautifulsoup4==4.12.2

# Additional utilities
pathlib2==2.3.7
