{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Google Play Store Multi-App Scraper\n",
    "\n",
    "This notebook scrapes app information and user reviews from Google Play Store for multiple apps and saves the data to CSV and XLSX formats.\n",
    "\n",
    "## Apps to scrape:\n",
    "- WhatsApp\n",
    "- Facebook\n",
    "- Instagram\n",
    "- Snapchat\n",
    "- Spotify"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import required libraries\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "from google_play_scraper import app, reviews, Sort\n",
    "import time\n",
    "import json\n",
    "from datetime import datetime\n",
    "import os\n",
    "from typing import List, Dict, Tuple\n",
    "from tqdm import tqdm\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "print(\"Libraries imported successfully!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Define app IDs to scrape\n",
    "app_ids = [\n",
    "    'com.whatsapp',\n",
    "    'com.facebook.katana',\n",
    "    'com.instagram.android',\n",
    "    'com.snapchat.android',\n",
    "    'com.spotify.music'\n",
    "]\n",
    "\n",
    "# Configuration\n",
    "COUNTRY = 'id'  # Indonesia\n",
    "LANG = 'id'     # Indonesian\n",
    "REVIEWS_PER_APP = 1000  # Number of reviews to scrape per app\n",
    "\n",
    "print(f\"Will scrape {len(app_ids)} apps with {REVIEWS_PER_APP} reviews each\")\n",
    "print(f\"App IDs: {app_ids}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Step 1: Scrape App Information"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def get_app_info(app_id: str, country: str = 'id', lang: str = 'id') -> Dict:\n",
    "    \"\"\"\n",
    "    Get detailed app information from Google Play Store.\n",
    "    \n",
    "    Args:\n",
    "        app_id: App package name\n",
    "        country: Country code\n",
    "        lang: Language code\n",
    "        \n",
    "    Returns:\n",
    "        Dictionary with app information\n",
    "    \"\"\"\n",
    "    try:\n",
    "        app_info = app(app_id, lang=lang, country=country)\n",
    "        \n",
    "        # Extract key information\n",
    "        return {\n",
    "            'app_id': app_id,\n",
    "            'title': app_info.get('title', ''),\n",
    "            'developer': app_info.get('developer', ''),\n",
    "            'developer_id': app_info.get('developerId', ''),\n",
    "            'category': app_info.get('genre', ''),\n",
    "            'rating': app_info.get('score', 0),\n",
    "            'rating_count': app_info.get('ratings', 0),\n",
    "            'installs': app_info.get('installs', ''),\n",
    "            'price': app_info.get('price', 0),\n",
    "            'free': app_info.get('free', True),\n",
    "            'size': app_info.get('size', ''),\n",
    "            'min_android': app_info.get('minInstalls', ''),\n",
    "            'content_rating': app_info.get('contentRating', ''),\n",
    "            'description': app_info.get('description', ''),\n",
    "            'summary': app_info.get('summary', ''),\n",
    "            'updated': app_info.get('updated', ''),\n",
    "            'version': app_info.get('version', ''),\n",
    "            'recent_changes': app_info.get('recentChanges', ''),\n",
    "            'scraped_at': datetime.now().isoformat()\n",
    "        }\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"Error getting app info for {app_id}: {e}\")\n",
    "        return {\n",
    "            'app_id': app_id,\n",
    "            'error': str(e),\n",
    "            'scraped_at': datetime.now().isoformat()\n",
    "        }\n",
    "\n",
    "# Scrape app information for all apps\n",
    "print(\"Scraping app information...\")\n",
    "apps_info = []\n",
    "\n",
    "for app_id in tqdm(app_ids, desc=\"Getting app info\"):\n",
    "    info = get_app_info(app_id, COUNTRY, LANG)\n",
    "    apps_info.append(info)\n",
    "    print(f\"✓ {info.get('title', app_id)} - Rating: {info.get('rating', 'N/A')}\")\n",
    "    time.sleep(1)  # Rate limiting\n",
    "\n",
    "# Convert to DataFrame\n",
    "apps_df = pd.DataFrame(apps_info)\n",
    "print(f\"\\nScraped information for {len(apps_df)} apps\")\n",
    "apps_df.head()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Step 2: Scrape User Reviews"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def scrape_app_reviews(app_id: str, count: int = 1000, country: str = 'id', lang: str = 'id') -> List[Dict]:\n",
    "    \"\"\"\n",
    "    Scrape reviews for a specific app.\n",
    "    \n",
    "    Args:\n",
    "        app_id: App package name\n",
    "        count: Number of reviews to scrape\n",
    "        country: Country code\n",
    "        lang: Language code\n",
    "        \n",
    "    Returns:\n",
    "        List of review dictionaries\n",
    "    \"\"\"\n",
    "    print(f\"\\nScraping {count} reviews for {app_id}...\")\n",
    "    \n",
    "    try:\n",
    "        all_reviews = []\n",
    "        continuation_token = None\n",
    "        batch_size = 200  # Reviews per batch\n",
    "        \n",
    "        with tqdm(total=count, desc=f\"Reviews for {app_id}\") as pbar:\n",
    "            while len(all_reviews) < count:\n",
    "                try:\n",
    "                    result, continuation_token = reviews(\n",
    "                        app_id,\n",
    "                        lang=lang,\n",
    "                        country=country,\n",
    "                        sort=Sort.NEWEST,\n",
    "                        count=min(batch_size, count - len(all_reviews)),\n",
    "                        continuation_token=continuation_token\n",
    "                    )\n",
    "                    \n",
    "                    if not result:\n",
    "                        print(f\"No more reviews available for {app_id}\")\n",
    "                        break\n",
    "                    \n",
    "                    # Add app_id to each review\n",
    "                    for review in result:\n",
    "                        review['app_id'] = app_id\n",
    "                        review['scraped_at'] = datetime.now().isoformat()\n",
    "                    \n",
    "                    all_reviews.extend(result)\n",
    "                    pbar.update(len(result))\n",
    "                    \n",
    "                    # Rate limiting\n",
    "                    time.sleep(1)\n",
    "                    \n",
    "                except Exception as e:\n",
    "                    print(f\"Error scraping batch for {app_id}: {e}\")\n",
    "                    break\n",
    "        \n",
    "        print(f\"Successfully scraped {len(all_reviews)} reviews for {app_id}\")\n",
    "        return all_reviews\n",
    "        \n",
    "    except Exception as e:\n",
    "        print(f\"Error scraping reviews for {app_id}: {e}\")\n",
    "        return []\n",
    "\n",
    "# Scrape reviews for all apps\n",
    "print(\"Starting review scraping process...\")\n",
    "all_reviews = []\n",
    "\n",
    "for app_id in app_ids:\n",
    "    app_reviews = scrape_app_reviews(app_id, REVIEWS_PER_APP, COUNTRY, LANG)\n",
    "    all_reviews.extend(app_reviews)\n",
    "    print(f\"Total reviews collected so far: {len(all_reviews)}\")\n",
    "    \n",
    "    # Longer pause between apps to avoid rate limiting\n",
    "    time.sleep(3)\n",
    "\n",
    "print(f\"\\nCompleted! Total reviews scraped: {len(all_reviews)}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Convert reviews to DataFrame\n",
    "if all_reviews:\n",
    "    reviews_df = pd.DataFrame(all_reviews)\n",
    "    \n",
    "    # Add additional features\n",
    "    reviews_df['review_length'] = reviews_df['content'].str.len()\n",
    "    reviews_df['word_count'] = reviews_df['content'].str.split().str.len()\n",
    "    \n",
    "    # Convert date columns\n",
    "    reviews_df['at'] = pd.to_datetime(reviews_df['at'])\n",
    "    \n",
    "    print(f\"Reviews DataFrame created with {len(reviews_df)} rows\")\n",
    "    print(f\"Columns: {list(reviews_df.columns)}\")\n",
    "    \n",
    "    # Show summary by app\n",
    "    print(\"\\nReviews by app:\")\n",
    "    print(reviews_df['app_id'].value_counts())\n",
    "    \n",
    "    reviews_df.head()\n",
    "else:\n",
    "    print(\"No reviews were scraped!\")\n",
    "    reviews_df = pd.DataFrame()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Step 3: Data Analysis and Summary"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create summary statistics\n",
    "if not reviews_df.empty and not apps_df.empty:\n",
    "    print(\"=\" * 60)\n",
    "    print(\"GOOGLE PLAY STORE SCRAPING SUMMARY\")\n",
    "    print(\"=\" * 60)\n",
    "    \n",
    "    print(f\"\\nApps scraped: {len(apps_df)}\")\n",
    "    print(f\"Total reviews: {len(reviews_df)}\")\n",
    "    print(f\"Date range: {reviews_df['at'].min()} to {reviews_df['at'].max()}\")\n",
    "    \n",
    "    print(\"\\nApp Information Summary:\")\n",
    "    for _, app in apps_df.iterrows():\n",
    "        if 'title' in app and 'rating' in app:\n",
    "            print(f\"  • {app['title']}: {app['rating']:.1f}★ ({app.get('rating_count', 'N/A')} ratings)\")\n",
    "    \n",
    "    print(\"\\nReview Statistics by App:\")\n",
    "    review_stats = reviews_df.groupby('app_id').agg({\n",
    "        'score': ['count', 'mean'],\n",
    "        'review_length': 'mean',\n",
    "        'word_count': 'mean'\n",
    "    }).round(2)\n",
    "    \n",
    "    review_stats.columns = ['Review Count', 'Avg Rating', 'Avg Length', 'Avg Words']\n",
    "    print(review_stats)\n",
    "    \n",
    "    print(\"\\nOverall Rating Distribution:\")\n",
    "    rating_dist = reviews_df['score'].value_counts().sort_index()\n",
    "    for rating, count in rating_dist.items():\n",
    "        percentage = (count / len(reviews_df)) * 100\n",
    "        print(f\"  {rating}★: {count} ({percentage:.1f}%)\")\n",
    "else:\n",
    "    print("No data available for summary")
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Step 4: Export Data to CSV and XLSX"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create timestamp for file naming\n",
    "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n",
    "\n",
    "# Export app information\n",
    "if not apps_df.empty:\n",
    "    # CSV export\n",
    "    apps_csv_filename = f\"google_play_apps_info_{timestamp}.csv\"\n",
    "    apps_df.to_csv(apps_csv_filename, index=False, encoding='utf-8')\n",
    "    print(f\"✓ App information saved to: {apps_csv_filename}\")\n",
    "    \n",
    "    # XLSX export\n",
    "    apps_xlsx_filename = f\"google_play_apps_info_{timestamp}.xlsx\"\n",
    "    apps_df.to_excel(apps_xlsx_filename, index=False, engine='openpyxl')\n",
    "    print(f\"✓ App information saved to: {apps_xlsx_filename}\")\n",
    "else:\n",
    "    print(\"❌ No app information to export\")\n",
    "\n",
    "# Export reviews\n",
    "if not reviews_df.empty:\n",
    "    # CSV export\n",
    "    reviews_csv_filename = f\"google_play_reviews_{timestamp}.csv\"\n",
    "    reviews_df.to_csv(reviews_csv_filename, index=False, encoding='utf-8')\n",
    "    print(f\"✓ Reviews saved to: {reviews_csv_filename}\")\n",
    "    \n",
    "    # XLSX export\n",
    "    reviews_xlsx_filename = f\"google_play_reviews_{timestamp}.xlsx\"\n",
    "    reviews_df.to_excel(reviews_xlsx_filename, index=False, engine='openpyxl')\n",
    "    print(f\"✓ Reviews saved to: {reviews_xlsx_filename}\")\n",
    "else:\n",
    "    print(\"❌ No reviews to export\")\n",
    "\n",
    "print(f\"\\nAll data exported with timestamp: {timestamp}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create a combined Excel file with multiple sheets\n",
    "if not apps_df.empty or not reviews_df.empty:\n",
    "    combined_filename = f\"google_play_complete_data_{timestamp}.xlsx\"\n",
    "    \n",
    "    with pd.ExcelWriter(combined_filename, engine='openpyxl') as writer:\n",
    "        if not apps_df.empty:\n",
    "            apps_df.to_excel(writer, sheet_name='App_Information', index=False)\n",
    "            print(f\"✓ App information added to sheet 'App_Information'\")\n",
    "        \n",
    "        if not reviews_df.empty:\n",
    "            reviews_df.to_excel(writer, sheet_name='Reviews', index=False)\n",
    "            print(f\"✓ Reviews added to sheet 'Reviews'\")\n",
    "            \n",
    "            # Create summary sheet\n",
    "            if not reviews_df.empty:\n",
    "                summary_data = []\n",
    "                \n",
    "                # Overall statistics\n",
    "                summary_data.append(['Metric', 'Value'])\n",
    "                summary_data.append(['Total Apps', len(apps_df) if not apps_df.empty else 0])\n",
    "                summary_data.append(['Total Reviews', len(reviews_df)])\n",
    "                summary_data.append(['Date Range Start', reviews_df['at'].min().strftime('%Y-%m-%d')])\n",
    "                summary_data.append(['Date Range End', reviews_df['at'].max().strftime('%Y-%m-%d')])\n",
    "                summary_data.append(['Average Rating', reviews_df['score'].mean()])\n",
    "                summary_data.append(['', ''])  # Empty row\n",
    "                \n",
    "                # Rating distribution\n",
    "                summary_data.append(['Rating Distribution', ''])\n",
    "                rating_dist = reviews_df['score'].value_counts().sort_index()\n",
    "                for rating, count in rating_dist.items():\n",
    "                    percentage = (count / len(reviews_df)) * 100\n",
    "                    summary_data.append([f'{rating} Stars', f'{count} ({percentage:.1f}%)'])\n",
    "                \n",
    "                summary_df = pd.DataFrame(summary_data)\n",
    "                summary_df.to_excel(writer, sheet_name='Summary', index=False, header=False)\n",
    "                print(f\"✓ Summary added to sheet 'Summary'\")\n",
    "    \n",
    "    print(f\"\\n🎉 Combined Excel file created: {combined_filename}\")\n",
    "    print(f\"   Contains: App Information, Reviews, and Summary sheets\")\n",
    "else:\n",
    "    print(\"❌ No data available to create combined file\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## Step 5: Data Validation and File Information"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Display file information and validation\n",
    "import os\n",
    "\n",
    "print(\"=\" * 60)\n",
    "print(\"FILE EXPORT SUMMARY\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# List all created files\n",
    "created_files = []\n",
    "for filename in os.listdir('.'):\n",
    "    if timestamp in filename and (filename.endswith('.csv') or filename.endswith('.xlsx')):\n",
    "        file_size = os.path.getsize(filename)\n",
    "        created_files.append((filename, file_size))\n",
    "\n",
    "if created_files:\n",
    "    print(\"\\nCreated files:\")\n",
    "    for filename, size in created_files:\n",
    "        size_mb = size / (1024 * 1024)\n",
    "        print(f\"  📄 {filename} ({size_mb:.2f} MB)\")\n",
    "    \n",
    "    print(f\"\\nTotal files created: {len(created_files)}\")\n",
    "    total_size = sum(size for _, size in created_files) / (1024 * 1024)\n",
    "    print(f\"Total size: {total_size:.2f} MB\")\n",
    "else:\n",
    "    print(\"❌ No files were created\")\n",
    "\n",
    "# Data validation summary\n",
    "print(\"\\n\" + \"=\" * 60)\n",
    "print(\"DATA VALIDATION SUMMARY\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "if not apps_df.empty:\n",
    "    print(f\"\\n✅ App Information Dataset:\")\n",
    "    print(f\"   • {len(apps_df)} apps scraped\")\n",
    "    print(f\"   • {len(apps_df.columns)} data fields per app\")\n",
    "    print(f\"   • Key fields: title, developer, rating, installs, category\")\n",
    "\n",
    "if not reviews_df.empty:\n",
    "    print(f\"\\n✅ Reviews Dataset:\")\n",
    "    print(f\"   • {len(reviews_df)} reviews scraped\")\n",
    "    print(f\"   • {len(reviews_df.columns)} data fields per review\")\n",
    "    print(f\"   • Average review length: {reviews_df['review_length'].mean():.0f} characters\")\n",
    "    print(f\"   • Average word count: {reviews_df['word_count'].mean():.0f} words\")\n",
    "    print(f\"   • Date range: {(reviews_df['at'].max() - reviews_df['at'].min()).days} days\")\n",
    "\n",
    "print(\"\\n🎉 Scraping completed successfully!\")\n",
    "print(\"\\n💡 Next steps:\")\n",
    "print(\"   • Open the Excel files to explore the data\")\n",
    "print(\"   • Use the CSV files for further analysis or machine learning\")\n",
    "print(\"   • Consider running sentiment analysis on the review content\")"
   ]
  }"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
