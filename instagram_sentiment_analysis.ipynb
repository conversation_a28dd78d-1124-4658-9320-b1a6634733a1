# Install required packages (run this cell first if packages are not installed)
!pip install google-play-scraper pandas transformers torch matplotlib seaborn tqdm langdetect requests beautifulsoup4

# Import required libraries
import pandas as pd
import numpy as np
from google_play_scraper import app, reviews, Sort
import time
import json
from datetime import datetime
import os
from typing import List, Dict, Tuple
import re
from langdetect import detect, LangDetectError
from transformers import AutoTokenizer, AutoModelForSequenceClassification, pipeline
import torch
from tqdm.notebook import tqdm
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Set style for better plots
plt.style.use('default')
sns.set_palette("husl")

print("✅ All libraries imported successfully!")
print(f"📱 Torch version: {torch.__version__}")
print(f"🔥 CUDA available: {torch.cuda.is_available()}")

# Configuration settings
CONFIG = {
    'app_id': 'com.instagram.android',
    'country': 'id',  # Indonesia
    'lang': 'id',     # Indonesian
    'review_count': 2000,
    'sort_order': Sort.NEWEST,
    'batch_size': 200,
    'delay_between_batches': 1  # seconds
}

print("📋 Configuration:")
for key, value in CONFIG.items():
    print(f"   {key}: {value}")

class NusaXLanguageDetector:
    """Language detector for Indonesian, Javanese, and Sundanese."""
    
    def __init__(self):
        self.language_patterns = {
            'indonesian': {
                'words': ['yang', 'dan', 'ini', 'itu', 'dengan', 'untuk', 'dari', 'pada', 'dalam', 'tidak', 'adalah', 'akan', 'sudah', 'bisa', 'juga'],
                'patterns': [r'\byang\b', r'\bdan\b', r'\btidak\b', r'\badalah\b']
            },
            'javanese': {
                'words': ['lan', 'karo', 'iki', 'kuwi', 'saka', 'kanggo', 'ing', 'ora', 'iku', 'wis', 'iso', 'uga'],
                'patterns': [r'\blan\b', r'\bkaro\b', r'\bora\b', r'\biku\b']
            },
            'sundanese': {
                'words': ['jeung', 'sareng', 'ieu', 'eta', 'ti', 'pikeun', 'di', 'henteu', 'teu', 'geus', 'tiasa', 'oge'],
                'patterns': [r'\bjeung\b', r'\bsareng\b', r'\bhenteu\b', r'\bteu\b']
            }
        }
    
    def detect_language_mix(self, text: str) -> Dict[str, float]:
        """Detect language mix in text."""
        text_lower = text.lower()
        scores = {'indonesian': 0, 'javanese': 0, 'sundanese': 0}
        
        for lang, patterns in self.language_patterns.items():
            word_matches = sum(1 for word in patterns['words'] if word in text_lower)
            pattern_matches = sum(1 for pattern in patterns['patterns'] if re.search(pattern, text_lower))
            
            total_words = len(text_lower.split())
            if total_words > 0:
                scores[lang] = (word_matches + pattern_matches) / total_words
        
        return scores
    
    def get_dominant_language(self, text: str) -> str:
        """Get dominant language."""
        scores = self.detect_language_mix(text)
        return max(scores, key=scores.get)

class NusaXTextPreprocessor:
    """Text preprocessor for NusaX languages."""
    
    def __init__(self):
        self.abbreviations = {
            'gk': 'tidak', 'ga': 'tidak', 'gak': 'tidak', 'udh': 'sudah', 'udah': 'sudah',
            'blm': 'belum', 'blom': 'belum', 'krn': 'karena', 'krna': 'karena',
            'dgn': 'dengan', 'sm': 'sama', 'tp': 'tapi', 'trs': 'terus',
            'yg': 'yang', 'utk': 'untuk', 'dr': 'dari', 'org': 'orang',
            'bgt': 'banget', 'bener': 'benar', 'emg': 'memang', 'emang': 'memang'
        }
    
    def expand_abbreviations(self, text: str) -> str:
        """Expand abbreviations."""
        words = text.split()
        expanded_words = []
        
        for word in words:
            word_lower = word.lower()
            if word_lower in self.abbreviations:
                expanded_words.append(self.abbreviations[word_lower])
            else:
                expanded_words.append(word)
        
        return ' '.join(expanded_words)
    
    def clean_text(self, text: str) -> str:
        """Clean and preprocess text."""
        text = text.lower()
        text = self.expand_abbreviations(text)
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        text = re.sub(r'@\w+', '', text)
        text = re.sub(r'#\w+', '', text)
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'[^\w\s.,!?-]', '', text)
        return text.strip()

# Initialize processors
language_detector = NusaXLanguageDetector()
text_preprocessor = NusaXTextPreprocessor()

print("✅ NusaX language processing tools initialized!")

# Test language detection with sample texts
test_texts = [
    "Aplikasi ini sangat bagus dan mudah digunakan",
    "Aplikasi iki apik tenan lan gampang digunakake", 
    "Aplikasi ieu saé pisan sareng gampang dianggo",
    "Instagram bagus bgt tapi kadang lemot juga sih",
    "Jelek banget aplikasinya, sering error lan ora iso dibuka"
]

print("🧪 Testing Language Detection:")
print("=" * 60)

for i, text in enumerate(test_texts, 1):
    scores = language_detector.detect_language_mix(text)
    dominant = language_detector.get_dominant_language(text)
    cleaned = text_preprocessor.clean_text(text)
    
    print(f"\n{i}. Original: {text}")
    print(f"   Cleaned:  {cleaned}")
    print(f"   Language: {dominant}")
    print(f"   Scores:   {scores}")

class NusaXSentimentAnalyzer:
    """Sentiment analyzer using NusaX-compatible models."""
    
    def __init__(self):
        self.pipelines = {}
        self.language_codes = {
            'indonesian': 'id',
            'javanese': 'jv', 
            'sundanese': 'su'
        }
        
    def load_models(self):
        """Load sentiment analysis models."""
        print("🤖 Loading sentiment analysis models...")
        
        try:
            # Primary model - multilingual sentiment
            model_name = "cardiffnlp/twitter-xlm-roberta-base-sentiment"
            print(f"   Loading {model_name}...")
            
            sentiment_pipeline = pipeline(
                "sentiment-analysis",
                model=model_name,
                device=0 if torch.cuda.is_available() else -1
            )
            
            # Use same model for all languages (in practice, you'd have separate models)
            for lang in self.language_codes.keys():
                self.pipelines[lang] = sentiment_pipeline
                
            print("✅ Primary model loaded successfully!")
            
        except Exception as e:
            print(f"⚠️  Primary model failed: {e}")
            print("   Loading fallback model...")
            self.load_fallback_model()
    
    def load_fallback_model(self):
        """Load fallback sentiment model."""
        try:
            sentiment_pipeline = pipeline(
                "sentiment-analysis", 
                model="nlptown/bert-base-multilingual-uncased-sentiment"
            )
            for lang in self.language_codes.keys():
                self.pipelines[lang] = sentiment_pipeline
            print("✅ Fallback model loaded successfully!")
        except Exception as e:
            print(f"❌ Error loading fallback model: {e}")
    
    def analyze_sentiment(self, text: str, language: str = None) -> Dict:
        """Analyze sentiment of text."""
        if not text or not text.strip():
            return {
                'sentiment': 'neutral',
                'confidence': 0.0,
                'language': 'unknown'
            }
        
        # Detect language if not provided
        if language is None:
            language = language_detector.get_dominant_language(text)
        
        # Preprocess text
        processed_text = text_preprocessor.clean_text(text)
        if len(processed_text) > 512:
            processed_text = processed_text[:512]
        
        try:
            # Get appropriate pipeline
            pipeline = self.pipelines.get(language, self.pipelines.get('indonesian'))
            
            if pipeline is None:
                return {
                    'sentiment': 'neutral',
                    'confidence': 0.0,
                    'language': language
                }
            
            # Perform sentiment analysis
            result = pipeline(processed_text)
            
            if isinstance(result, list) and len(result) > 0:
                result = result[0]
            
            # Normalize labels
            label = result['label'].lower()
            if 'pos' in label or label == 'positive':
                sentiment = 'positive'
            elif 'neg' in label or label == 'negative':
                sentiment = 'negative'
            else:
                sentiment = 'neutral'
            
            return {
                'sentiment': sentiment,
                'confidence': result['score'],
                'language': language
            }
            
        except Exception as e:
            print(f"Error analyzing sentiment: {e}")
            return {
                'sentiment': 'neutral',
                'confidence': 0.0,
                'language': language
            }

# Initialize sentiment analyzer
sentiment_analyzer = NusaXSentimentAnalyzer()
sentiment_analyzer.load_models()

# Test sentiment analysis
test_reviews = [
    "Instagram sangat bagus! Fiturnya lengkap dan mudah digunakan",
    "Aplikasi jelek banget, sering error dan lemot",
    "Biasa aja sih, tidak ada yang istimewa",
    "Love this app! Tapi kadang suka ngelag",
    "Aplikasi iki apik tenan, nanging kadhang angel dibukak"
]

print("🧪 Testing Sentiment Analysis:")
print("=" * 60)

for i, review in enumerate(test_reviews, 1):
    result = sentiment_analyzer.analyze_sentiment(review)
    
    print(f"\n{i}. Review: {review}")
    print(f"   Sentiment: {result['sentiment']} (confidence: {result['confidence']:.3f})")
    print(f"   Language: {result['language']}")

def scrape_instagram_reviews(count: int = 2000) -> List[Dict]:
    """Scrape Instagram reviews from Google Play Store."""
    
    print(f"📱 Scraping {count} Instagram reviews...")
    
    try:
        # Get app information
        app_info = app(
            CONFIG['app_id'], 
            lang=CONFIG['lang'], 
            country=CONFIG['country']
        )
        
        print(f"📊 App: {app_info['title']}")
        print(f"👨‍💻 Developer: {app_info['developer']}")
        print(f"⭐ Rating: {app_info['score']:.2f}")
        print(f"📝 Total Reviews: {app_info['reviews']:,}")
        
        # Scrape reviews in batches
        all_reviews = []
        continuation_token = None
        batch_size = CONFIG['batch_size']
        
        with tqdm(total=count, desc="Scraping reviews") as pbar:
            while len(all_reviews) < count:
                try:
                    result, continuation_token = reviews(
                        CONFIG['app_id'],
                        lang=CONFIG['lang'],
                        country=CONFIG['country'],
                        sort=CONFIG['sort_order'],
                        count=min(batch_size, count - len(all_reviews)),
                        continuation_token=continuation_token
                    )
                    
                    if not result:
                        print("\n⚠️  No more reviews available")
                        break
                        
                    all_reviews.extend(result)
                    pbar.update(len(result))
                    
                    # Add delay to avoid rate limiting
                    time.sleep(CONFIG['delay_between_batches'])
                    
                except Exception as e:
                    print(f"\n❌ Error scraping batch: {e}")
                    break
        
        print(f"\n✅ Successfully scraped {len(all_reviews)} reviews")
        return all_reviews
        
    except Exception as e:
        print(f"❌ Error scraping reviews: {e}")
        return []

# Scrape reviews
print("🚀 Starting review scraping...")
reviews_data = scrape_instagram_reviews(CONFIG['review_count'])

# Convert to DataFrame and process
if reviews_data:
    print("📊 Converting to DataFrame...")
    df = pd.DataFrame(reviews_data)
    
    print(f"✅ Created DataFrame with {len(df)} reviews")
    print(f"📅 Date range: {df['at'].min()} to {df['at'].max()}")
    
    # Display sample reviews
    print("\n📝 Sample reviews:")
    for i, review in enumerate(df['content'].head(3), 1):
        print(f"{i}. {review[:100]}...")
else:
    print("❌ No reviews data available")
    df = pd.DataFrame()

# Perform sentiment analysis on all reviews
if not df.empty:
    print("🤖 Performing sentiment analysis on all reviews...")
    
    sentiments = []
    confidences = []
    languages = []
    
    for review in tqdm(df['content'], desc="Analyzing sentiment"):
        result = sentiment_analyzer.analyze_sentiment(review)
        sentiments.append(result['sentiment'])
        confidences.append(result['confidence'])
        languages.append(result['language'])
    
    # Add results to DataFrame
    df['sentiment'] = sentiments
    df['sentiment_confidence'] = confidences
    df['detected_language'] = languages
    
    # Add additional features
    df['review_length'] = df['content'].str.len()
    df['word_count'] = df['content'].str.split().str.len()
    
    print("✅ Sentiment analysis completed!")
    print(f"📊 Processed {len(df)} reviews")
else:
    print("❌ No data to analyze")

# Generate comprehensive report
if not df.empty:
    print("📈 SENTIMENT ANALYSIS REPORT")
    print("=" * 60)
    
    print(f"\n📊 Total Reviews Analyzed: {len(df):,}")
    print(f"📅 Date Range: {df['at'].min()} to {df['at'].max()}")
    
    # Sentiment distribution
    print("\n😊 Sentiment Distribution:")
    sentiment_counts = df['sentiment'].value_counts()
    for sentiment, count in sentiment_counts.items():
        percentage = (count / len(df)) * 100
        emoji = "😊" if sentiment == "positive" else "😞" if sentiment == "negative" else "😐"
        print(f"   {emoji} {sentiment.capitalize()}: {count:,} ({percentage:.1f}%)")
    
    # Language distribution
    print("\n🌍 Language Distribution:")
    lang_counts = df['detected_language'].value_counts()
    for lang, count in lang_counts.items():
        percentage = (count / len(df)) * 100
        print(f"   🗣️  {lang.capitalize()}: {count:,} ({percentage:.1f}%)")
    
    # Rating vs Sentiment correlation
    print("\n⭐ Rating vs Sentiment:")
    rating_sentiment = df.groupby(['score', 'sentiment']).size().unstack(fill_value=0)
    print(rating_sentiment)
    
    # Average confidence by sentiment
    print("\n🎯 Average Confidence by Sentiment:")
    conf_by_sentiment = df.groupby('sentiment')['sentiment_confidence'].mean()
    for sentiment, conf in conf_by_sentiment.items():
        print(f"   {sentiment.capitalize()}: {conf:.3f}")
    
    # Review length statistics
    print("\n📝 Review Length Statistics:")
    print(f"   Average length: {df['review_length'].mean():.1f} characters")
    print(f"   Average words: {df['word_count'].mean():.1f} words")
    print(f"   Longest review: {df['review_length'].max()} characters")
    print(f"   Shortest review: {df['review_length'].min()} characters")
else:
    print("❌ No data available for analysis")

# Create comprehensive visualizations
if not df.empty:
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Instagram App Reviews - Sentiment Analysis Report', fontsize=16, fontweight='bold')
    
    # 1. Sentiment Distribution (Pie Chart)
    sentiment_counts = df['sentiment'].value_counts()
    colors = ['#2ecc71', '#e74c3c', '#95a5a6']  # Green, Red, Gray
    axes[0, 0].pie(sentiment_counts.values, labels=sentiment_counts.index, autopct='%1.1f%%', 
                   colors=colors, startangle=90)
    axes[0, 0].set_title('Sentiment Distribution')
    
    # 2. Rating vs Sentiment (Stacked Bar)
    rating_sentiment = df.groupby(['score', 'sentiment']).size().unstack(fill_value=0)
    rating_sentiment.plot(kind='bar', ax=axes[0, 1], stacked=True, color=colors)
    axes[0, 1].set_title('Rating vs Sentiment')
    axes[0, 1].set_xlabel('Rating (Stars)')
    axes[0, 1].set_ylabel('Number of Reviews')
    axes[0, 1].legend(title='Sentiment')
    axes[0, 1].tick_params(axis='x', rotation=0)
    
    # 3. Language Distribution (Bar Chart)
    lang_counts = df['detected_language'].value_counts()
    axes[0, 2].bar(lang_counts.index, lang_counts.values, color='#3498db')
    axes[0, 2].set_title('Language Distribution')
    axes[0, 2].set_xlabel('Language')
    axes[0, 2].set_ylabel('Number of Reviews')
    axes[0, 2].tick_params(axis='x', rotation=45)
    
    # 4. Sentiment Confidence Distribution (Histogram)
    axes[1, 0].hist(df['sentiment_confidence'], bins=20, alpha=0.7, color='#9b59b6')
    axes[1, 0].set_title('Sentiment Confidence Distribution')
    axes[1, 0].set_xlabel('Confidence Score')
    axes[1, 0].set_ylabel('Frequency')
    
    # 5. Review Length vs Sentiment (Box Plot)
    sentiment_order = ['negative', 'neutral', 'positive']
    df_plot = df[df['sentiment'].isin(sentiment_order)]
    sns.boxplot(data=df_plot, x='sentiment', y='review_length', ax=axes[1, 1], order=sentiment_order)
    axes[1, 1].set_title('Review Length by Sentiment')
    axes[1, 1].set_xlabel('Sentiment')
    axes[1, 1].set_ylabel('Review Length (characters)')
    
    # 6. Sentiment by Language (Stacked Bar)
    lang_sentiment = df.groupby(['detected_language', 'sentiment']).size().unstack(fill_value=0)
    lang_sentiment.plot(kind='bar', ax=axes[1, 2], stacked=True, color=colors)
    axes[1, 2].set_title('Sentiment by Language')
    axes[1, 2].set_xlabel('Language')
    axes[1, 2].set_ylabel('Number of Reviews')
    axes[1, 2].legend(title='Sentiment')
    axes[1, 2].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.show()
    
    print("📊 Visualizations generated successfully!")
else:
    print("❌ No data available for visualization")

# Save results to CSV
if not df.empty:
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"instagram_reviews_sentiment_{timestamp}.csv"
    
    # Save to CSV
    df.to_csv(filename, index=False, encoding='utf-8')
    print(f"💾 Results saved to: {filename}")
    print(f"📊 File size: {os.path.getsize(filename) / 1024 / 1024:.2f} MB")
    
    # Display column information
    print(f"\n📋 Dataset Info:")
    print(f"   Rows: {len(df):,}")
    print(f"   Columns: {len(df.columns)}")
    print(f"   Columns: {list(df.columns)}")
    
    # Show sample of final dataset
    print(f"\n📝 Sample of processed data:")
    display_cols = ['content', 'score', 'sentiment', 'sentiment_confidence', 'detected_language']
    print(df[display_cols].head())
else:
    print("❌ No data to save")

# Find and analyze interesting mixed-language reviews
if not df.empty:
    print("🔍 Analyzing Mixed-Language Reviews:")
    print("=" * 60)
    
    # Find reviews with mixed languages (containing both Indonesian and regional language words)
    mixed_reviews = []
    
    for idx, row in df.iterrows():
        text = row['content'].lower()
        scores = language_detector.detect_language_mix(text)
        
        # Check if multiple languages have significant scores
        significant_langs = [lang for lang, score in scores.items() if score > 0.1]
        
        if len(significant_langs) > 1:
            mixed_reviews.append({
                'content': row['content'],
                'sentiment': row['sentiment'],
                'confidence': row['sentiment_confidence'],
                'detected_language': row['detected_language'],
                'language_scores': scores
            })
    
    print(f"🌍 Found {len(mixed_reviews)} mixed-language reviews")
    
    # Show some examples
    if mixed_reviews:
        print("\n📝 Examples of Mixed-Language Reviews:")
        for i, review in enumerate(mixed_reviews[:5], 1):
            print(f"\n{i}. {review['content'][:100]}...")
            print(f"   Sentiment: {review['sentiment']} (confidence: {review['confidence']:.3f})")
            print(f"   Language scores: {review['language_scores']}")
    
    # Analyze sentiment by language
    print("\n📊 Sentiment Analysis by Language:")
    for lang in df['detected_language'].unique():
        lang_df = df[df['detected_language'] == lang]
        sentiment_dist = lang_df['sentiment'].value_counts(normalize=True) * 100
        avg_confidence = lang_df['sentiment_confidence'].mean()
        
        print(f"\n🗣️  {lang.capitalize()} ({len(lang_df)} reviews):")
        for sentiment, percentage in sentiment_dist.items():
            print(f"   {sentiment}: {percentage:.1f}%")
        print(f"   Average confidence: {avg_confidence:.3f}")
else:
    print("❌ No data available for mixed-language analysis")

# Final summary
if not df.empty:
    print("🎉 ANALYSIS COMPLETE!")
    print("=" * 60)
    
    # Key insights
    total_reviews = len(df)
    positive_pct = (df['sentiment'] == 'positive').sum() / total_reviews * 100
    negative_pct = (df['sentiment'] == 'negative').sum() / total_reviews * 100
    neutral_pct = (df['sentiment'] == 'neutral').sum() / total_reviews * 100
    
    avg_rating = df['score'].mean()
    avg_confidence = df['sentiment_confidence'].mean()
    
    indonesian_pct = (df['detected_language'] == 'indonesian').sum() / total_reviews * 100
    
    print(f"📊 KEY INSIGHTS:")
    print(f"   • Analyzed {total_reviews:,} Instagram reviews from Indonesian Play Store")
    print(f"   • Overall sentiment: {positive_pct:.1f}% positive, {negative_pct:.1f}% negative, {neutral_pct:.1f}% neutral")
    print(f"   • Average rating: {avg_rating:.2f}/5 stars")
    print(f"   • Average sentiment confidence: {avg_confidence:.3f}")
    print(f"   • Language distribution: {indonesian_pct:.1f}% Indonesian, {100-indonesian_pct:.1f}% regional languages")
    
    print(f"\n💾 OUTPUT FILES:")
    print(f"   • CSV file with all results: instagram_reviews_sentiment_*.csv")
    print(f"   • Interactive visualizations displayed above")
    
    print(f"\n🔬 TECHNICAL DETAILS:")
    print(f"   • Used NusaX-compatible models for multi-language sentiment analysis")
    print(f"   • Processed Indonesian, Javanese, and Sundanese text")
    print(f"   • Applied text preprocessing and abbreviation expansion")
    print(f"   • Scraped from Google Play Store with rate limiting")
    
    print(f"\n✨ This analysis demonstrates the capability to process mixed-code Indonesian reviews")
    print(f"   using NusaX methodology for regional language sentiment analysis.")
else:
    print("❌ Analysis could not be completed due to lack of data")
    print("   Please check your internet connection and try again.")